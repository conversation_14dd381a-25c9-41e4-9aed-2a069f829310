# Deployment Guide - Antosa Architect

## 🚀 Deploy ke Vercel

### Prerequisites
1. Install Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Login ke Vercel:
   ```bash
   vercel login
   ```

### Deployment Steps

1. **Clone/Navigate ke project directory**:
   ```bash
   cd c:\xampp\htdocs\antosa-architect
   ```

2. **Deploy ke Vercel**:
   ```bash
   vercel
   ```

3. **Follow the prompts**:
   - Set up and deploy? `Y`
   - Which scope? (pilih account Anda)
   - Link to existing project? `N` (untuk deployment pertama)
   - What's your project's name? `antosa-architect`
   - In which directory is your code located? `./`

### 📁 File Konfigurasi

#### `vercel.json`
```json
{
  "functions": {
    "public/index.php": {
      "runtime": "vercel-php@0.6.0"
    }
  },
  "routes": [
    {
      "src": "/assets/(.*)",
      "dest": "/public/assets/$1"
    },
    {
      "src": "/(.*\\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))",
      "dest": "/public/$1"
    },
    {
      "src": "/(.*)",
      "dest": "/public/index.php"
    }
  ]
}
```

### ✅ Fitur yang Sudah Dikonfigurasi

- ✅ PHP Runtime support
- ✅ Static assets routing
- ✅ Dynamic site URL detection
- ✅ Environment-aware routing
- ✅ Optimized file exclusion

### 🔧 Environment Variables (Optional)

Jika diperlukan, Anda bisa menambahkan environment variables di Vercel dashboard:
- `SITE_NAME`
- `COMPANY_EMAIL`
- `COMPANY_PHONE`

### 📝 Notes

1. **Database**: Jika menggunakan database, pertimbangkan menggunakan:
   - PlanetScale (MySQL)
   - Supabase (PostgreSQL)
   - MongoDB Atlas

2. **File Uploads**: Untuk file uploads, gunakan:
   - Cloudinary
   - AWS S3
   - Vercel Blob

3. **Custom Domain**: Setelah deploy, Anda bisa menambahkan custom domain di Vercel dashboard.

### 🚨 Troubleshooting

- Jika ada error 404, pastikan routing di `vercel.json` sudah benar
- Jika static assets tidak load, periksa path di `public/assets/`
- Untuk debugging, gunakan `vercel logs` untuk melihat server logs
